#!/bin/bash
set -euo pipefail

# Script to update ECS service with new container image and wait for steady state

# Global array to track temporary files for cleanup
TEMP_FILES=()
CLEANUP_DONE=false

# Cleanup function to remove temporary files
cleanup() {
  # Prevent multiple cleanup calls
  if [[ "$CLEANUP_DONE" == true ]]; then
    return 0
  fi

  CLEANUP_DONE=true

  if [ ${#TEMP_FILES[@]} -gt 0 ]; then
    # Remove duplicates from TEMP_FILES array
    local unique_files=($(printf '%s\n' "${TEMP_FILES[@]}" | sort -u))
    log_info "🧹 Cleaning up ${#unique_files[@]} temporary files..."
    rm -f "${unique_files[@]}" 2>/dev/null || true
  fi
}

# Set trap to ensure cleanup on exit
trap cleanup EXIT ERR SIGINT SIGTERM

# --- Default Configuration ---
PROFILE="sandbox"
REGION="us-east-1"
SERVICE_TYPE=""  # Will be set by -s flag (ml or ai)
USE_PROFILE=true  # Will be set to false in CI/CD environments

# Deployment timing
POLL_INTERVAL_SECONDS=15
MAX_ATTEMPTS=60  # ~15 minutes total wait time

# Health check configuration
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Task management
FORCE_STOP_TASKS=true
STOP_TASKS_TIMEOUT=300

# --- Helper Functions ---
log() {
    local level=$1
    local message=$2
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    echo "[${timestamp}] [${level}] ${message}"
}

log_info() {
    log "INFO" "$1"
}

log_warn() {
    log "WARN" "$1" >&2
}

log_error() {
    log "ERROR" "$1" >&2
    # Don't exit automatically to allow for cleanup
    # Caller should handle exit if needed
}

# --- Environment Detection ---
detect_environment() {
    # Check if running in GitHub Actions
    if [[ -n "${GITHUB_ACTIONS:-}" ]]; then
        log_info "🔍 Detected GitHub Actions environment"
        USE_PROFILE=false
        return 0
    fi

    # Check if running in other CI/CD environments
    if [[ -n "${CI:-}" ]] || [[ -n "${CONTINUOUS_INTEGRATION:-}" ]]; then
        log_info "🔍 Detected CI/CD environment"
        USE_PROFILE=false
        return 0
    fi

    # Check if AWS credentials are available as environment variables
    if [[ -n "${AWS_ACCESS_KEY_ID:-}" && -n "${AWS_SECRET_ACCESS_KEY:-}" ]]; then
        log_info "🔍 Detected AWS credentials in environment variables"
        USE_PROFILE=false
        return 0
    fi

    # Check if AWS session token is available (OIDC/STS)
    if [[ -n "${AWS_SESSION_TOKEN:-}" ]]; then
        log_info "🔍 Detected AWS session token (OIDC/STS)"
        USE_PROFILE=false
        return 0
    fi

    # Check if running on EC2 with IAM role
    if curl -s --max-time 2 http://169.254.169.254/latest/meta-data/iam/security-credentials/ >/dev/null 2>&1; then
        log_info "🔍 Detected EC2 instance with IAM role"
        USE_PROFILE=false
        return 0
    fi

    # Default to using profile for local development
    log_info "🔍 Using AWS profile for local development"
    USE_PROFILE=true
    return 0
}

# --- Service Configuration ---
get_service_config() {
    local service=$1
    local env=$2

    case $service in
        ml)
            CONTAINER_NAME="machinelearning"
            SERVICE_NAME="20degrees-${env}-ml-service"
            TASK_DEFINITION_FAMILY="20degrees-${env}-machinelearning"
            HEALTH_CHECK_PATH="/machinelearning/livecheck"
            ;;
        ai)
            CONTAINER_NAME="aiagent"
            SERVICE_NAME="20degrees-${env}-aiagent-service"
            TASK_DEFINITION_FAMILY="20degrees-${env}-aiagent"
            HEALTH_CHECK_PATH="/agent/healthcheck"
            ;;
        *)
            log_error "Unsupported service type: $service. Must be one of: ml, ai"
            return 1
            ;;
    esac
}

# --- ECR Configuration ---
get_ecr_uri() {
    local env=$1
    local tag=$2
    local service=$3

    case $service in
        ml)
            case $env in
                sandbox)
                    echo "052188048271.dkr.ecr.us-east-1.amazonaws.com/20degrees-ml-sandbox:${tag}"
                    ;;
                production)
                    echo "118006903084.dkr.ecr.us-east-1.amazonaws.com/20degrees-ml-production:${tag}"
                    ;;
                *)
                    log_error "Unsupported environment: $env. Must be one of: sandbox, production"
                    return 1
                    ;;
            esac
            ;;
        ai)
            case $env in
                sandbox)
                    echo "052188048271.dkr.ecr.us-east-1.amazonaws.com/20degrees-ai-sandbox:${tag}"
                    ;;
                production)
                    echo "118006903084.dkr.ecr.us-east-1.amazonaws.com/20degrees-ai-production:${tag}"
                    ;;
                *)
                    log_error "Unsupported environment: $env. Must be one of: sandbox, production"
                    return 1
                    ;;
            esac
            ;;
        *)
            log_error "Unsupported service type: $service. Must be one of: ml, ai"
            return 1
            ;;
    esac
}

# --- Parse Command Line Arguments ---
while getopts "e:t:s:p:r:" opt; do
  case $opt in
    e) ENVIRONMENT="$OPTARG" ;;
    t) IMAGE_TAG="$OPTARG" ;;
    s) SERVICE_TYPE="$OPTARG" ;;
    p) PROFILE="$OPTARG" ;;
    r) REGION="$OPTARG" ;;
    *) echo "Usage: $0 -e <environment> -t <image-tag> -s <service-type> [-p aws-profile] [-r aws-region]" >&2
       echo "Service types: ml (Machine Learning), ai (AI Agent)" >&2
       echo "Example: $0 -e sandbox -t latest -s ml" >&2
       echo "Example: $0 -e sandbox -t latest -s ai" >&2
       exit 1 ;;
  esac
done

# Validate required arguments
if [ -z "${ENVIRONMENT:-}" ] || [ -z "${IMAGE_TAG:-}" ] || [ -z "${SERVICE_TYPE:-}" ]; then
  echo "Error: -e (environment), -t (image-tag), and -s (service-type) are all required" >&2
  echo "Usage: $0 -e <environment> -t <image-tag> -s <service-type> [-p aws-profile] [-r aws-region]" >&2
  echo "Service types: ml (Machine Learning), ai (AI Agent)" >&2
  echo "Example: $0 -e sandbox -t latest -s ml" >&2
  echo "Example: $0 -e sandbox -t latest -s ai" >&2
  exit 1
fi

# Validate service type
if [[ "$SERVICE_TYPE" != "ml" && "$SERVICE_TYPE" != "ai" ]]; then
  echo "Error: Invalid service type '$SERVICE_TYPE'. Must be 'ml' or 'ai'" >&2
  exit 1
fi

# Detect environment and set authentication method
detect_environment

# Set service-specific configuration
get_service_config "$SERVICE_TYPE" "$ENVIRONMENT"
if [ $? -ne 0 ]; then
  exit 1
fi

# Set environment-specific configuration
CLUSTER_NAME="20degrees-${ENVIRONMENT}-ml-cluster"  # Both services use the same cluster
HEALTH_CHECK_URL="https://services.${ENVIRONMENT}.healthtechgate.com${HEALTH_CHECK_PATH}"

# Set the full image URI
NEW_IMAGE_URI=$(get_ecr_uri "$ENVIRONMENT" "$IMAGE_TAG" "$SERVICE_TYPE")

# Validate environment and service type are supported
if [ -z "$NEW_IMAGE_URI" ]; then
  exit 1
fi

# AWS CLI helper with error handling
run_aws() {
    local cmd=("aws")
    local expect_json=false
    local output_format=""
    local query=""
    local output_file=""
    
    # Process arguments to detect if we expect JSON output
    for arg in "$@"; do
        if [[ "$arg" == "--output" || "$arg" == "--query" ]]; then
            expect_json=true
        fi
        if [[ "$arg" == "--output" ]]; then
            output_format="${@:$((OPTIND+1)):1}"
        fi
        if [[ "$arg" == "--query" ]]; then
            query="${@:$((OPTIND+1)):1}"
        fi
    done
    
    # If no output format is specified and we're likely to get JSON, default to json
    if [[ -z "$output_format" && ("$*" == *"describe-"* || "$*" == *"list-"*) ]]; then
        expect_json=true
        cmd+=("--output" "json")
    fi
    
    # Add profile and region based on environment
    if [[ "$USE_PROFILE" == true && -n "$PROFILE" ]]; then
        cmd+=("--profile" "$PROFILE")
    fi
    cmd+=("--region" "$REGION" "$@")
    
    log_info "Executing: ${cmd[*]}"
    
    # Create temp file for output if needed
    local temp_output
    temp_output=$(mktemp)
    TEMP_FILES+=("$temp_output")
    
    # Execute the command and capture both stdout and stderr
    if ! "${cmd[@]}" > "$temp_output" 2>&1; then
        local error_output
        error_output=$(cat "$temp_output")
        
        # Check for SSO token expiration
        if [[ "$error_output" == *"Token has expired"* ]]; then
            log_error "AWS SSO token has expired. Please run: aws sso login --profile $PROFILE"
            return 1
        fi
        
        log_error "AWS command failed: $error_output"
        return 1
    fi
    
    # Check if output is valid JSON if we expect JSON
    if [[ "$expect_json" == true && "$output_format" != "text" ]]; then
        # Skip validation for empty responses
        if [[ -s "$temp_output" ]]; then
            if ! jq empty "$temp_output" 2>/dev/null; then
                log_error "Invalid JSON response from AWS:"
                cat "$temp_output"
                return 1
            fi
        fi
    fi
    
    # Output the result
    cat "$temp_output"
    return 0
}

# Get current task definition and update image
get_updated_task_definition() {
    log_info "📋 Fetching current task definition for family: ${TASK_DEFINITION_FAMILY}"
    
    # Create a temporary file for the task definition
    local temp_file updated_temp_file final_temp_file
    temp_file=$(mktemp)
    updated_temp_file=$(mktemp)
    final_temp_file=$(mktemp)
    
    # Add to cleanup array
    TEMP_FILES+=("$temp_file" "$updated_temp_file" "$final_temp_file")
    
    # Get the current task definition using AWS CLI directly
    log_info "🔍 Fetching task definition using AWS CLI"
    
    # Run AWS CLI directly to avoid any potential issues with the helper function
    if [[ "$USE_PROFILE" == true && -n "$PROFILE" ]]; then
        aws --profile "$PROFILE" --region "$REGION" ecs describe-task-definition \
            --task-definition "${TASK_DEFINITION_FAMILY}" \
            --query 'taskDefinition' > "$temp_file"
    else
        aws --region "$REGION" ecs describe-task-definition \
            --task-definition "${TASK_DEFINITION_FAMILY}" \
            --query 'taskDefinition' > "$temp_file"
    fi
    
    if [ $? -ne 0 ]; then
        log_error "❌ Failed to get task definition"
        return 1
    fi
    
    # Verify file exists and has content
    if [ ! -s "$temp_file" ]; then
        log_error "❌ Task definition file is empty"
        return 1
    fi
    
    # Debug: Show file size and content type
    log_info "📊 Task definition file size: $(wc -c < "$temp_file") bytes"
    log_info "📋 First 10 lines of task definition:"
    head -n 10 "$temp_file"
    
    # Validate containerDefinitions exists
    log_info "🔍 Validating containerDefinitions in task definition"
    
    # Debug: Show the first 200 characters of the task definition
    log_info "Task definition preview (first 200 chars):"
    head -c 200 "$temp_file"
    
    # Check if the file is valid JSON
    if ! jq empty "$temp_file" 2>/dev/null; then
        log_error "❌ Invalid JSON in task definition file"
        log_error "File contents:"
        cat "$temp_file"
        return 1
    fi
    
    # Check if containerDefinitions exists and get its length
    if ! container_count=$(jq '.containerDefinitions | length' "$temp_file" 2>/dev/null); then
        log_error "❌ Invalid task definition - could not parse containerDefinitions"
        log_error "Task definition structure:"
        jq 'keys' "$temp_file"
        return 1
    fi
    
    if [ "$container_count" -eq 0 ]; then
        log_error "❌ Invalid task definition - no containers found"
        return 1
    fi
    
    # Check if our container exists in the task definition
    if ! jq -e --arg container "$CONTAINER_NAME" '.containerDefinitions[] | select(.name == $container)' "$temp_file" >/dev/null 2>&1; then
        log_error "❌ Container '$CONTAINER_NAME' not found in task definition"
        log_error "Available containers:"
        jq -r '.containerDefinitions[].name' "$temp_file"
        return 1
    fi
    
    log_info "🔄 Updating container '${CONTAINER_NAME}' with new image: ${NEW_IMAGE_URI}"
    
    # Update the container image
    if ! jq --arg container "$CONTAINER_NAME" --arg image "$NEW_IMAGE_URI" '
        .containerDefinitions = [
            .containerDefinitions[] | 
            if .name == $container then 
                .image = $image 
            else 
                . 
            end
        ]
    ' "$temp_file" > "$updated_temp_file"; then
        log_error "❌ Failed to update container image"
        return 1
    fi
    
    # Clean up the task definition by removing fields that can't be in the register call
    if ! jq 'del(
        .taskDefinitionArn,
        .revision,
        .status,
        .requiresAttributes,
        .compatibilities,
        .registeredAt,
        .registeredBy,
        .deregisteredAt
    )' "$updated_temp_file" > "$final_temp_file"; then
        log_error "❌ Failed to clean up task definition"
        return 1
    fi
    
    # Validate the final JSON
    if ! jq empty "$final_temp_file" 2>/dev/null; then
        log_error "❌ Generated invalid JSON for task definition"
        log_error "First 200 chars of generated JSON:"
        head -c 200 "$final_temp_file"
        return 1
    fi
    
    # Show a preview of the changes
    log_info "📝 Task definition changes preview:"
    jq -r --arg container "$CONTAINER_NAME" --arg image "$NEW_IMAGE_URI" '
        "Container: " + $container + 
        "\nOld Image: " + (.containerDefinitions[] | select(.name == $container) | .image) + 
        "\nNew Image: " + $image
    ' "$temp_file"
    
    log_info "✅ Successfully updated task definition"
    
    # Return the path to the final task definition file
    echo "$final_temp_file"
}

# Register new task definition with updated image
register_new_task_definition() {
    log_info "📝 Registering new task definition revision..."
    
    # Create temporary files for the task definition
    local temp_file updated_temp_file final_temp_file
    temp_file=$(mktemp)
    updated_temp_file=$(mktemp)
    final_temp_file=$(mktemp)
    TEMP_FILES+=("$temp_file" "$updated_temp_file" "$final_temp_file")
    
    # Get the current task definition using AWS CLI directly
    log_info "🔍 Fetching task definition using AWS CLI"
    
    # Run AWS CLI directly to avoid any potential issues with the helper function
    if [[ "$USE_PROFILE" == true && -n "$PROFILE" ]]; then
        aws --profile "$PROFILE" --region "$REGION" ecs describe-task-definition \
            --task-definition "${TASK_DEFINITION_FAMILY}" \
            --query 'taskDefinition' > "$temp_file"
    else
        aws --region "$REGION" ecs describe-task-definition \
            --task-definition "${TASK_DEFINITION_FAMILY}" \
            --query 'taskDefinition' > "$temp_file"
    fi
    
    if [ $? -ne 0 ]; then
        log_error "❌ Failed to get task definition"
        return 1
    fi
    
    # Verify file exists and has content
    if [ ! -s "$temp_file" ]; then
        log_error "❌ Task definition file is empty"
        return 1
    fi
    
    # Debug: Show file size and content type
    log_info "📊 Task definition file size: $(wc -c < "$temp_file") bytes"
    log_info "📋 First 10 lines of task definition:"
    head -n 10 "$temp_file"
    
    # Check if the file is valid JSON
    if ! jq empty "$temp_file" 2>/dev/null; then
        log_error "❌ Invalid JSON in task definition file"
        log_error "File contents:"
        cat "$temp_file"
        return 1
    fi
    
    # Check if containerDefinitions exists and get its length
    if ! container_count=$(jq '.containerDefinitions | length' "$temp_file" 2>/dev/null); then
        log_error "❌ Invalid task definition - could not parse containerDefinitions"
        log_error "Task definition structure:"
        jq 'keys' "$temp_file"
        return 1
    fi
    
    if [ "$container_count" -eq 0 ]; then
        log_error "❌ Invalid task definition - no containers found"
        return 1
    fi
    
    # Check if our container exists in the task definition
    if ! jq -e --arg container "$CONTAINER_NAME" '.containerDefinitions[] | select(.name == $container)' "$temp_file" >/dev/null 2>&1; then
        log_error "❌ Container '$CONTAINER_NAME' not found in task definition"
        log_error "Available containers:"
        jq -r '.containerDefinitions[].name' "$temp_file"
        return 1
    fi
    
    log_info "🔄 Updating container '${CONTAINER_NAME}' with new image: ${NEW_IMAGE_URI}"
    
    # Update the container image
    if ! jq --arg container "$CONTAINER_NAME" --arg image "$NEW_IMAGE_URI" '
        .containerDefinitions = [
            .containerDefinitions[] | 
            if .name == $container then 
                .image = $image 
            else 
                . 
            end
        ]
    ' "$temp_file" > "$updated_temp_file"; then
        log_error "❌ Failed to update container image"
        return 1
    fi
    
    # Clean up the task definition by removing fields that can't be in the register call
    if ! jq 'del(
        .taskDefinitionArn,
        .revision,
        .status,
        .requiresAttributes,
        .compatibilities,
        .registeredAt,
        .registeredBy,
        .deregisteredAt
    )' "$updated_temp_file" > "$final_temp_file"; then
        log_error "❌ Failed to clean up task definition"
        return 1
    fi
    
    # Validate the final JSON
    if ! jq empty "$final_temp_file" 2>/dev/null; then
        log_error "❌ Generated invalid JSON for task definition"
        log_error "First 200 chars of generated JSON:"
        head -c 200 "$final_temp_file"
        return 1
    fi
    
    # Show a preview of the changes
    log_info "📝 Task definition changes preview:"
    jq -r --arg container "$CONTAINER_NAME" --arg image "$NEW_IMAGE_URI" '
        "Container: " + $container + 
        "\nOld Image: " + (.containerDefinitions[] | select(.name == $container) | .image) + 
        "\nNew Image: " + $image
    ' "$temp_file"
    
    log_info "✅ Successfully updated task definition"
    
    # Use the final task definition file
    local task_def_file="$final_temp_file"
    
    # Debug: Log the task definition structure
    log_info "Task definition structure:"
    jq '{family, cpu, memory, containerDefinitions: [.containerDefinitions[] | {name, image}]}' "$task_def_file"
    
    # Register the updated task definition using AWS CLI directly
    log_info "Registering task definition with AWS ECS"
    
    local new_task_def_json
    local temp_output
    temp_output=$(mktemp)
    TEMP_FILES+=("$temp_output")
    
    # Run AWS CLI directly to avoid any potential issues with the helper function
    if [[ "$USE_PROFILE" == true && -n "$PROFILE" ]]; then
        aws --profile "$PROFILE" --region "$REGION" ecs register-task-definition \
            --cli-input-json "file://$task_def_file" \
            --query 'taskDefinition' > "$temp_output"
    else
        aws --region "$REGION" ecs register-task-definition \
            --cli-input-json "file://$task_def_file" \
            --query 'taskDefinition' > "$temp_output"
    fi
    
    # Check if the command succeeded
    if [ $? -ne 0 ]; then
        log_error "Failed to register task definition"
        log_error "AWS CLI output:"
        cat "$temp_output"
        return 1
    fi
    
    # Read the output file
    new_task_def_json=$(cat "$temp_output")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to register task definition"
        return 1
    fi
    
    # Extract the task definition ARN
    local new_task_def_arn
    new_task_def_arn=$(jq -r '.taskDefinitionArn // ""' "$temp_output")
    
    if [ -z "$new_task_def_arn" ] || [[ ! "$new_task_def_arn" =~ ^arn:aws:ecs: ]]; then
        log_error "Invalid task definition ARN received: '$new_task_def_arn'"
        log_error "Full response:"
        cat "$temp_output" | jq .
        return 1
    fi
    
    # Log success
    log_info "✅ Successfully registered new task definition: ${new_task_def_arn}"
    
    # Return the ARN in a format that can be easily captured
    echo "TASK_DEF_ARN:$new_task_def_arn"
}

# Health check function
check_application_health() {
  local retries=0
  while [[ $retries -lt $HEALTH_CHECK_RETRIES ]]; do
    log_info "🏥 Checking application health (attempt $((retries + 1))/$HEALTH_CHECK_RETRIES)..."

    # Perform health check with timeout
    response=$(curl -s -w "%{http_code}" --max-time "$HEALTH_CHECK_TIMEOUT" "$HEALTH_CHECK_URL" 2>/dev/null)
    http_code="${response: -3}"
    body="${response%???}"

    if [[ "$http_code" == "200" ]]; then
      log_info "✅ Application health check passed! Response: $body"
      return 0
    else
      log_warn "⚠️ Health check failed. HTTP code: $http_code, Response: $body"
      ((retries++))
      if [[ $retries -lt $HEALTH_CHECK_RETRIES ]]; then
        sleep 5  # Wait 5 seconds before retry
      fi
    fi
  done

  log_error "❌ Application health check failed after $HEALTH_CHECK_RETRIES attempts"
  return 1
}

# Force stop existing tasks function
force_stop_existing_tasks() {
  if [[ "$FORCE_STOP_TASKS" != "true" ]]; then
    log_info "⏭️ Skipping force stop of existing tasks (FORCE_STOP_TASKS=false)"
    return 0
  fi

  log_info "🛑 Force stopping existing tasks before deployment..."

  # Get list of running tasks
  if [[ "$USE_PROFILE" == true && -n "$PROFILE" ]]; then
    task_arns=$(aws ecs list-tasks \
      --cluster "$CLUSTER_NAME" \
      --service-name "$SERVICE_NAME" \
      --desired-status RUNNING \
      --profile "$PROFILE" \
      --region "$REGION" \
      --query "taskArns" \
      --output text 2>/dev/null)
  else
    task_arns=$(aws ecs list-tasks \
      --cluster "$CLUSTER_NAME" \
      --service-name "$SERVICE_NAME" \
      --desired-status RUNNING \
      --region "$REGION" \
      --query "taskArns" \
      --output text 2>/dev/null)
  fi

  if [[ -z "$task_arns" || "$task_arns" == "None" ]]; then
    log_info "ℹ️ No running tasks found to stop"
    return 0
  fi

  # Convert space-separated string to array
  task_array=($task_arns)
  log_info "📋 Found ${#task_array[@]} running task(s) to stop"

  # Stop each task
  for task_arn in "${task_array[@]}"; do
    task_id=$(basename "$task_arn")
    log_info "🛑 Stopping task: $task_id"

    if [[ "$USE_PROFILE" == true && -n "$PROFILE" ]]; then
      aws ecs stop-task \
        --cluster "$CLUSTER_NAME" \
        --task "$task_arn" \
        --reason "Force stop before deployment" \
        --profile "$PROFILE" \
        --region "$REGION" >/dev/null 2>&1
    else
      aws ecs stop-task \
        --cluster "$CLUSTER_NAME" \
        --task "$task_arn" \
        --reason "Force stop before deployment" \
        --region "$REGION" >/dev/null 2>&1
    fi

    if [[ $? -eq 0 ]]; then
      log_info "✅ Stop command sent for task: $task_id"
    else
      log_warn "⚠️ Failed to send stop command for task: $task_id"
    fi
  done

  # Wait for tasks to stop
  log_info "⏳ Waiting for tasks to stop (timeout: ${STOP_TASKS_TIMEOUT}s)..."
  local start_time=$(date +%s)
  local elapsed=0
  
  while [ $elapsed -lt $STOP_TASKS_TIMEOUT ]; do
    # Get running tasks using run_aws
    local running_tasks_json
    running_tasks_json=$(run_aws ecs list-tasks \
        --cluster "$CLUSTER_NAME" \
        --service-name "$SERVICE_NAME" \
        --desired-status RUNNING)
        
    # Get pending tasks using run_aws
    local pending_tasks_json
    pending_tasks_json=$(run_aws ecs list-tasks \
        --cluster "$CLUSTER_NAME" \
        --service-name "$SERVICE_NAME" \
        --desired-status PENDING)
    
    # Parse task counts using jq with error handling
    local running_count pending_count
    
    # Check if running_tasks_json is valid JSON before parsing
    if echo "$running_tasks_json" | jq empty 2>/dev/null; then
        running_count=$(echo "$running_tasks_json" | jq '.taskArns | length // 0')
    else
        log_warn "⚠️ Invalid JSON response for running tasks. Setting count to 0."
        running_count=0
    fi
    
    # Check if pending_tasks_json is valid JSON before parsing
    if echo "$pending_tasks_json" | jq empty 2>/dev/null; then
        pending_count=$(echo "$pending_tasks_json" | jq '.taskArns | length // 0')
    else
        log_warn "⚠️ Invalid JSON response for pending tasks. Setting count to 0."
        pending_count=0
    fi
    
    # If no tasks are running or pending, we're done
    if [[ "$running_count" -eq 0 && "$pending_count" -eq 0 ]]; then
        log_info "✅ No existing tasks found. Ready to deploy."
        return 0
    fi
    
    log_info "🔍 Found $running_count running tasks and $pending_count pending tasks."
    
    # Stop each running task if there are any
    if [[ "$running_count" -gt 0 ]]; then
        log_info "Stopping running tasks..."
        
        # Extract task ARNs using jq with error handling
        local task_arns=""
        if echo "$running_tasks_json" | jq empty 2>/dev/null; then
            task_arns=$(echo "$running_tasks_json" | jq -r '.taskArns[]' 2>/dev/null || echo "")
        else
            log_warn "⚠️ Invalid JSON response when extracting task ARNs. Skipping task stopping."
        fi
        
        for task_arn in $task_arns; do
            log_info "  ⛔ Stopping task: $task_arn"
            run_aws ecs stop-task \
                --cluster "$CLUSTER_NAME" \
                --task "$task_arn" > /dev/null
        done
    fi
    
    # If only pending tasks remain, they're probably stopping
    if [[ "$running_count" -eq 0 && "$pending_count" -gt 0 ]]; then
        log_info "⏳ $pending_count tasks are still in PENDING state. Waiting for them to stop..."
    fi
    
    # Wait a bit before checking again
    sleep 5
    
    # Update elapsed time
    local current_time=$(date +%s)
    elapsed=$((current_time - start_time))
    
    # Show progress
    log_info "⏳ Waited ${elapsed}s/$STOP_TASKS_TIMEOUT for tasks to stop..."
  done
  
  log_error "❌ Timeout waiting for tasks to stop after ${STOP_TASKS_TIMEOUT}s"
  return 1
}

# --- Main Script ---

echo ""
log_info "🚀 Starting ECS service update"
log_info "   ├─ Service Type: $SERVICE_TYPE ($([ "$SERVICE_TYPE" = "ml" ] && echo "Machine Learning" || echo "AI Agent"))"
log_info "   ├─ Environment: $ENVIRONMENT"
log_info "   ├─ Authentication: $([ "$USE_PROFILE" = true ] && echo "AWS Profile ($PROFILE)" || echo "Environment Variables/IAM Role")"
log_info "   ├─ Service: $SERVICE_NAME"
log_info "   ├─ Cluster: $CLUSTER_NAME"
log_info "   ├─ Container: $CONTAINER_NAME"
log_info "   ├─ Health Check: $HEALTH_CHECK_URL"
log_info "   └─ Image: $NEW_IMAGE_URI"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Validate AWS credentials are available in CI/CD environment
if [[ "$USE_PROFILE" == false ]]; then
    log_info "🔐 Validating AWS credentials in CI/CD environment..."

    # Debug: Show current AWS environment variables (without exposing sensitive values)
    log_info "🔍 AWS Environment Debug:"
    log_info "   ├─ AWS_REGION: ${AWS_REGION:-'not set'}"
    log_info "   ├─ AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-'not set'}"
    log_info "   ├─ AWS_ACCESS_KEY_ID: $([ -n "${AWS_ACCESS_KEY_ID:-}" ] && echo "set (${#AWS_ACCESS_KEY_ID} chars)" || echo "not set")"
    log_info "   ├─ AWS_SECRET_ACCESS_KEY: $([ -n "${AWS_SECRET_ACCESS_KEY:-}" ] && echo "set (${#AWS_SECRET_ACCESS_KEY} chars)" || echo "not set")"
    log_info "   ├─ AWS_SESSION_TOKEN: $([ -n "${AWS_SESSION_TOKEN:-}" ] && echo "set (${#AWS_SESSION_TOKEN} chars)" || echo "not set")"
    log_info "   └─ AWS_PROFILE: ${AWS_PROFILE:-'not set'}"

    # Retry logic for AWS credentials validation
    max_retries=5
    retry_delay=3
    attempt=1
    validation_success=false

    while [[ $attempt -le $max_retries ]]; do
        log_info "🔄 Credentials validation attempt $attempt/$max_retries..."

        # Create temp file for AWS output
        aws_output_file=$(mktemp)
        TEMP_FILES+=("$aws_output_file")

        # Try to get AWS caller identity to verify credentials work
        if aws sts get-caller-identity --region "$REGION" > "$aws_output_file" 2>&1; then
            # Parse the caller identity response
            account_id=$(jq -r '.Account // "unknown"' "$aws_output_file" 2>/dev/null || echo "unknown")
            user_id=$(jq -r '.UserId // "unknown"' "$aws_output_file" 2>/dev/null || echo "unknown")
            arn=$(jq -r '.Arn // "unknown"' "$aws_output_file" 2>/dev/null || echo "unknown")

            log_info "✅ AWS credentials validated successfully"
            log_info "🔍 AWS Identity Information:"
            log_info "   ├─ Account ID: $account_id"
            log_info "   ├─ User ID: $user_id"
            log_info "   └─ ARN: $arn"

            validation_success=true
            break
        else
            error_output=$(cat "$aws_output_file")

            log_warn "⚠️ Credentials validation attempt $attempt failed"
            log_warn "   Error: $error_output"

            # Check for specific error types
            if [[ "$error_output" == *"Unable to locate credentials"* ]]; then
                log_warn "   Issue: No AWS credentials found"
            elif [[ "$error_output" == *"Token has expired"* ]]; then
                log_warn "   Issue: AWS token has expired"
            elif [[ "$error_output" == *"Access Denied"* ]]; then
                log_warn "   Issue: Access denied - check IAM permissions"
            elif [[ "$error_output" == *"InvalidUserID.NotFound"* ]]; then
                log_warn "   Issue: Invalid user ID or role"
            else
                log_warn "   Issue: Unknown AWS authentication error"
            fi

            if [[ $attempt -lt $max_retries ]]; then
                log_info "⏳ Waiting ${retry_delay}s before retry..."
                sleep $retry_delay
            fi

            ((attempt++))
        fi
    done

    if [[ "$validation_success" != true ]]; then
        log_error "❌ AWS credentials validation failed after $max_retries attempts"
        log_error ""
        log_error "🔧 Troubleshooting Steps:"
        log_error "   1. Verify OIDC configuration in GitHub Actions:"
        log_error "      - name: Configure AWS credentials"
        log_error "        uses: aws-actions/configure-aws-credentials@v4"
        log_error "        with:"
        log_error "          role-to-assume: arn:aws:iam::ACCOUNT:role/GitHubActionsRole"
        log_error "          aws-region: us-east-1"
        log_error ""
        log_error "   2. Check IAM role permissions and trust policy"
        log_error "   3. Verify the role ARN is correct for the target account"
        log_error "   4. Ensure the GitHub repository is authorized to assume the role"
        log_error ""
        log_error "   Alternative: Use Access Keys (less secure):"
        log_error "   - Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables"
        exit 1
    fi
fi

# 2. Update service with new task definition
register_output=$(register_new_task_definition)

if [ $? -ne 0 ]; then
  log_error "Failed to register new task definition"
  exit 1
fi

# Extract the task definition ARN from the output
NEW_TASK_DEFINITION_ARN=$(echo "$register_output" | grep "TASK_DEF_ARN:" | cut -d':' -f2-)

if [ -z "$NEW_TASK_DEFINITION_ARN" ]; then
  log_error "Failed to extract task definition ARN from output"
  log_error "Output: $register_output"
  exit 1
fi

# Debug: Verify the task definition ARN
log_info "🔍 Using task definition ARN: $NEW_TASK_DEFINITION_ARN"

log_info "🚫 Force stopping existing tasks before deployment..."
if [ "$FORCE_STOP_TASKS" = true ]; then
  force_stop_existing_tasks
fi

log_info "🚀 Initiating service update with new task definition..."

# Update the service with the new task definition
if [[ "$USE_PROFILE" == true && -n "$PROFILE" ]]; then
  aws --profile "$PROFILE" --region "$REGION" ecs update-service \
    --cluster "$CLUSTER_NAME" \
    --service "$SERVICE_NAME" \
    --task-definition "$NEW_TASK_DEFINITION_ARN" \
    --force-new-deployment
else
  aws --region "$REGION" ecs update-service \
    --cluster "$CLUSTER_NAME" \
    --service "$SERVICE_NAME" \
    --task-definition "$NEW_TASK_DEFINITION_ARN" \
    --force-new-deployment
fi

if [ $? -ne 0 ]; then
  log_error "Failed to update service with new task definition"
  exit 1
fi

log_info "✅ Service update initiated with new task definition"

log_info "✅ Service update initiated. Now monitoring deployment status..."
echo ""
log_info "⏱️  Monitoring Configuration:"
log_info "   ├─ Poll Interval: $POLL_INTERVAL_SECONDS seconds"
log_info "   ├─ Max Attempts: $MAX_ATTEMPTS"
log_info "   └─ Health Check URL: $HEALTH_CHECK_URL"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Debug: Show current environment before monitoring
log_info "🔍 Pre-monitoring debug information:"
log_info "   ├─ SERVICE_NAME: $SERVICE_NAME"
log_info "   ├─ CLUSTER_NAME: $CLUSTER_NAME"
log_info "   ├─ USE_PROFILE: $USE_PROFILE"
log_info "   ├─ PROFILE: ${PROFILE:-'not set'}"
log_info "   ├─ REGION: $REGION"
log_info "   └─ AWS_PROFILE env: ${AWS_PROFILE:-'not set'}"

# 3. Monitor deployment status
attempts=0
log_info "🚀 Starting monitoring loop..."
log_info "⏳ Waiting 5 seconds for ECS to register the service update..."
sleep 5

# Debug: Check variables before loop
log_info "🔍 Debug: attempts=$attempts, MAX_ATTEMPTS=$MAX_ATTEMPTS"
log_info "🔍 Debug: About to start while loop..."

# Temporarily disable strict error handling for monitoring loop
set +e
while [[ $attempts -lt $MAX_ATTEMPTS ]]; do
  ((attempts++))
  log_info "🔎 Attempt $attempts/$MAX_ATTEMPTS: Checking service status..."

  # Get service description with error handling
  service_status_temp_file=$(mktemp)
  TEMP_FILES+=("$service_status_temp_file")

  log_info "🔍 Fetching service status for: $SERVICE_NAME in cluster: $CLUSTER_NAME"

  if [[ "$USE_PROFILE" == true && -n "$PROFILE" ]]; then
    if ! aws ecs describe-services \
      --cluster "$CLUSTER_NAME" \
      --services "$SERVICE_NAME" \
      --profile "$PROFILE" \
      --region "$REGION" \
      --query "services[0]" > "$service_status_temp_file" 2>&1; then
      log_warn "⚠️ Failed to retrieve service status with profile. Error:"
      cat "$service_status_temp_file"
      log_warn "⚠️ Retrying in $POLL_INTERVAL_SECONDS seconds..."
      sleep "$POLL_INTERVAL_SECONDS"
      continue
    fi
  else
    if ! aws ecs describe-services \
      --cluster "$CLUSTER_NAME" \
      --services "$SERVICE_NAME" \
      --region "$REGION" \
      --query "services[0]" > "$service_status_temp_file" 2>&1; then
      log_warn "⚠️ Failed to retrieve service status. Error:"
      cat "$service_status_temp_file"
      log_warn "⚠️ Retrying in $POLL_INTERVAL_SECONDS seconds..."
      sleep "$POLL_INTERVAL_SECONDS"
      continue
    fi
  fi

  service_status_json=$(cat "$service_status_temp_file")

  if [[ -z "$service_status_json" || "$service_status_json" == "null" ]]; then
    log_warn "⚠️ Empty or null service status response. Retrying..."
    sleep "$POLL_INTERVAL_SECONDS"
    continue
  fi

  # Parse the service status using jq
  desired_count=$(echo "$service_status_json" | jq '.desiredCount // 0')
  running_count=$(echo "$service_status_json" | jq '.runningCount // 0')
  pending_count=$(echo "$service_status_json" | jq '.pendingCount // 0')
  service_overall_status=$(echo "$service_status_json" | jq -r '.status // "UNKNOWN"')

  # Extract primary deployment information directly from the service_status_json
  # This avoids additional API calls since we already have the data
  primary_deployment=$(echo "$service_status_json" | jq '.deployments[] | select(.status=="PRIMARY")')
  
  if [ -z "$primary_deployment" ]; then
    log_warn "⚠️ No PRIMARY deployment found. Service may be in transition."
    primary_deployment_running_count=0
    primary_deployment_desired_count=0
    primary_deployment_rollout_state="UNKNOWN"
  else
    primary_deployment_running_count=$(echo "$primary_deployment" | jq '.runningCount // 0')
    primary_deployment_desired_count=$(echo "$primary_deployment" | jq '.desiredCount // 0')
    primary_deployment_rollout_state=$(echo "$primary_deployment" | jq -r '.rolloutState // "UNKNOWN"')
  fi

  # Count active deployments (not COMPLETED)
  active_deployments_count=$(echo "$service_status_json" | jq '[.deployments[] | select(.rolloutState!="COMPLETED")] | length // 0')


  # Beautified status display
  log_info "📊 ECS Service Status:"
  log_info "   ├─ Service Status: $service_overall_status"
  log_info "   ├─ Tasks: $running_count/$desired_count running, $pending_count pending"
  log_info "   ├─ Primary Deployment: $primary_deployment_running_count/$primary_deployment_desired_count ($primary_deployment_rollout_state)"
  log_info "   └─ Active Deployments: $active_deployments_count"

  # Early health check: If we have running tasks, try health check even if ECS isn't fully steady
  if [[ "$service_overall_status" == "ACTIVE" && "$running_count" -gt 0 && "$primary_deployment_running_count" -gt 0 ]]; then
    log_info "🔍 Service has running tasks. Performing early health check..."
    if check_application_health; then
      echo ""
      echo "🎉 EARLY SUCCESS! Application is ready!"
      echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
      log_info "✅ Application health check passed"
      log_info "ℹ️  ECS may still be stabilizing, but application is responding"

      # Test service-specific functionality
      if [[ "$SERVICE_TYPE" == "ml" ]]; then
        log_info "🧪 Testing ML service functionality..."
        test_response=$(curl -s --max-time 10 "https://services.sandbox.healthtechgate.com/machinelearning/test-url-parsing?url=20degrees-sandbox/images/users/316/test.png" 2>/dev/null)
        if [[ $? -eq 0 ]]; then
          log_info "✅ ML service test: PASSED"
        else
          log_warn "⚠️ ML service test: FAILED (deployment still successful)"
        fi
      elif [[ "$SERVICE_TYPE" == "ai" ]]; then
        log_info "🧪 Testing AI Agent service functionality..."
        test_response=$(curl -s --max-time 10 "$HEALTH_CHECK_URL" 2>/dev/null)
        if [[ $? -eq 0 ]]; then
          log_info "✅ AI Agent service test: PASSED"
        else
          log_warn "⚠️ AI Agent service test: FAILED (deployment still successful)"
        fi
      fi

      echo ""
      log_info "🚀 Deployment completed successfully!"
      echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
      exit 0
    else
      log_info "⏳ Early health check failed. Continuing to wait for ECS steady state..."
    fi
  fi


  # --- Steady State Check ---
  # A common definition of steady state:
  # 1. Service status is ACTIVE.
  # 2. The running count of the PRIMARY deployment equals its desired count.
  # 3. The rolloutState of the PRIMARY deployment is COMPLETED.
  # 4. The overall service running count equals its desired count.
  # 5. There are no other deployments still in progress (i.e., only 1 deployment which is PRIMARY and COMPLETED, or count of deployments != COMPLETED is 0)

  # Simplified check: Is there only one deployment (the new one), is it PRIMARY, is its rolloutState COMPLETED, and runningCount == desiredCount?
  # A more robust check would also verify event messages for any failures.

  if [[ "$service_overall_status" == "ACTIVE" && \
        "$primary_deployment_rollout_state" == "COMPLETED" && \
        "$primary_deployment_running_count" -eq "$primary_deployment_desired_count" && \
        "$running_count" -eq "$desired_count" && \
        "$active_deployments_count" -eq 0 ]]; then
    log_info "🎉 Service '$SERVICE_NAME' has reached ECS steady state!"

    # Final check on events for any recent errors (optional but good practice)
    log_info "🔍 Verifying recent service events for any deployment failures..."
    
    # Get service events using run_aws
    service_events_json=$(run_aws ecs describe-services \
        --cluster "$CLUSTER_NAME" \
        --services "$SERVICE_NAME" \
        --query "services[0].events")
    
    # Parse events with jq to find errors
    recent_error_events=$(echo "$service_events_json" | jq -r 'map(select(.message | contains("failed") or contains("error"))) | .[0:5] | map(.createdAt + " " + .message) | .[]')

    if [[ -n "$recent_error_events" ]]; then
        log_warn "⚠️ Recent events found that might indicate issues, please review:"
        echo "$recent_error_events"
        log_info "⏳ Continuing to monitor for application health despite ECS events..."
    else
        log_info "👍 No recent critical error events found in the last few messages."
    fi

    # Now check application health
    log_info "🏥 ECS deployment complete. Verifying application health..."
    if check_application_health; then
      echo ""
      echo "🎉 DEPLOYMENT COMPLETE! Full success achieved!"
      echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
      log_info "✅ ECS service: STEADY STATE"
      log_info "✅ Application health: HEALTHY"

      # Test service-specific functionality
      if [[ "$SERVICE_TYPE" == "ml" ]]; then
        log_info "🧪 Testing ML service functionality..."
        test_response=$(curl -s --max-time 10 "https://services.sandbox.healthtechgate.com/machinelearning/test-url-parsing?url=20degrees-sandbox/images/users/316/test.png" 2>/dev/null)
        if [[ $? -eq 0 ]]; then
          log_info "✅ ML service test: PASSED"
        else
          log_warn "⚠️ ML service test: FAILED (deployment still successful)"
        fi
      elif [[ "$SERVICE_TYPE" == "ai" ]]; then
        log_info "🧪 Testing AI Agent service functionality..."
        test_response=$(curl -s --max-time 10 "$HEALTH_CHECK_URL" 2>/dev/null)
        if [[ $? -eq 0 ]]; then
          log_info "✅ AI Agent service test: PASSED"
        else
          log_warn "⚠️ AI Agent service test: FAILED (deployment still successful)"
        fi
      fi

      echo ""
      log_info "🚀 All systems operational!"
      echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
      exit 0
    else
      log_error "❌ ECS deployment completed but application health check failed"
      log_error "   The service may still be starting up. Please check application logs."
      exit 1
    fi
  fi

  log_info "⏳ Deployment still in progress. Waiting for $POLL_INTERVAL_SECONDS seconds..."
  sleep "$POLL_INTERVAL_SECONDS"
done

# Re-enable strict error handling
set -e

log_error "❌ Timeout: Service '$SERVICE_NAME' did not reach a steady state after $MAX_ATTEMPTS attempts."
log_error ""
log_error "📊 Final Status Report:"
log_error "   ├─ Service Status: $service_overall_status"
log_error "   ├─ Tasks: $running_count/$desired_count running, $pending_count pending"
log_error "   ├─ Primary Deployment: $primary_deployment_running_count/$primary_deployment_desired_count ($primary_deployment_rollout_state)"
log_error "   └─ Active Deployments: $active_deployments_count"
log_error ""
log_error "🔍 Troubleshooting:"
log_error "   ├─ Check ECS console: https://console.aws.amazon.com/ecs/home?region=$REGION#/clusters/$CLUSTER_NAME/services/$SERVICE_NAME"
log_error "   ├─ Check logs: aws logs tail /ecs/your-log-group --follow"
log_error "   └─ Manual health check: curl $HEALTH_CHECK_URL"
exit 1