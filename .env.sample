# Environment variables for AIAgent

# OpenAI API Key
OPENAI_API_KEY=
# OpenSearch Host URL
OPENSEARCH_HOST=

# (Optional) MongoDB URI if used
MONGO_URI=
TRACKERS_STATIC_DATA_LAMBDA=trackers-static-sandbox-function
# (Optional) AWS credentials for Lambda/SQS if running locally
AWS_ACCESS_KEY_ID=  
AWS_SECRET_ACCESS_KEY=
AWS_REGION=

# (Optional) JWT Secret for auth
JWT_SECRET=

# (Optional) Other service configuration
# SERVICE_X_API_KEY=your-service-x-api-key
ENVIRONMENT=
NEW_RELIC_LICENSE_KEY=
