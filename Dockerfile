FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Install minimal system dependencies, time synchronization tools, and uv
RUN apt-get update --fix-missing && \
    apt-get install -y --no-install-recommends \
    curl \
    tzdata \
    ntp \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    pip install --no-cache-dir uv

COPY requirements.txt /tmp/requirements.txt
RUN uv pip install --system --no-cache -r /tmp/requirements.txt && \
    rm /tmp/requirements.txt

# Create non-root user and set up directories
RUN useradd -m appuser

# Set working directory and create app directories with proper ownership
WORKDIR /app
RUN mkdir -p /app/data /app/logs && \
    chown -R appuser:appuser /app

# Copy application code
COPY . .


# Expose port for FastAPI
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/agent/healthcheck || exit 1

# Run the application directly with uvicorn (New Relic is initialized in the code)
CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]