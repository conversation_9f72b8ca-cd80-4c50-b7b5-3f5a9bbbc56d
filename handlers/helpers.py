import os
import sys
from pathlib import Path
from datetime import datetime
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers import PydanticOutputParser
from dotenv import load_dotenv

# Load environment variables from .env file
env_path = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) / '.env'
load_dotenv(dotenv_path=env_path)

# Initialize OpenAI
try:
    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
    if not OPENAI_API_KEY:
        print("OPENAI_API_KEY environment variable not found or empty")
        print("Please make sure your .env file contains a valid OPENAI_API_KEY")
        print(f"Checked for .env file at: {env_path}")
        sys.exit(1)
except Exception as e:
    print(f"Error loading environment variables: {str(e)}")
    sys.exit(1)

# Define structured output schema
class DateRange(BaseModel):
    start_date: str = Field(..., description="Start date in YYYY-MM-DD format")
    end_date: str = Field(..., description="End date in YYYY-MM-DD format")

# Create the output parser
parser = PydanticOutputParser(pydantic_object=DateRange)

# Define LLM prompt to extract dates
date_prompt = PromptTemplate(
    template="""Extract the start and end date from the user's query considering today's date is {today_date}.

    Query: {query}
    
    {format_instructions}
    
    Response should be in JSON format only without explanation.
    """,
    input_variables=["query"],
    partial_variables={
        "format_instructions": parser.get_format_instructions(),
        "today_date": datetime.today().strftime("%Y-%m-%d"),
    },
)

# LLM to interpret the date range
llm = ChatOpenAI(model_name="gpt-4.1-mini", temperature=0, openai_api_key=OPENAI_API_KEY)

def extract_dates_llm(query: str):
    """Uses OpenAI GPT to extract structured date ranges from a natural language query."""
    formatted_query = date_prompt.format(query=query)

    print("Query: ", formatted_query)
    response = llm.invoke(formatted_query)

    print("Response: ", response.content)
    return parser.parse(response.content)

class NudgeResponse(BaseModel):
    title: str = Field(..., description="Short, engaging title of the message")
    message: str = Field(..., description="Personalized motivational, insight, or anomaly message")
    priority: str = Field(..., description="Priority of the message: low, medium, or high")

nudge_parser = PydanticOutputParser(pydantic_object=NudgeResponse)

def extract_response_message(response: str):
    return nudge_parser.parse(response).model_dump()

