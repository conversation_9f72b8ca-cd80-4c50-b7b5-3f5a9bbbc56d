import os
import time
from typing import Dict

import jwt

def decodeJWT(token: str) -> dict:
    try:
        # Get JWT secret from environment variables
        jwt_secret = os.environ.get("JWT_SECRET", "secret")

        # Ensure token is properly formatted
        if isinstance(token, str):
            # No need to encode the token as PyJWT handles string tokens correctly
            pass

        # Decode the token with proper error handling
        decoded_token = jwt.decode(token, jwt_secret, algorithms=["HS256"])

        # Verify token expiration
        if decoded_token["exp"] >= time.time():
            return decoded_token
        else:
            print(f"JWT token expired: {decoded_token['exp']} < {time.time()}")
            return None
    except Exception as e:
        print(f"JWT decode error: {e}")
        return {}