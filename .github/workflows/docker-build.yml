name: Docker Build and Push

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'sandbox'
        type: choice
        options:
          - sandbox
          - production
      image_tag:
        description: 'Image tag to use (default: sandbox or commit hash)'
        required: false
        type: string
      architecture:
        description: 'Architecture to build for'
        required: true
        default: 'arm64'
        type: choice
        options:
          - arm64
          - amd64

env:
  SANDBOX_ECR: ************.dkr.ecr.us-east-1.amazonaws.com/20degrees-ai-sandbox
  PRODUCTION_ECR: ************.dkr.ecr.us-east-1.amazonaws.com/20degrees-ai-sandbox
  SANDBOX_ACCOUNT_ID: "************"
  PRODUCTION_ACCOUNT_ID: "************"

jobs:
  build-and-push:
    name: Build and Push Image
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read

    strategy:
      matrix:
        arch: [arm64]

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Set environment variables
        id: env-vars
        run: |
          # Determine environment from workflow_dispatch input or set default to sandbox for other triggers
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            ENVIRONMENT="${{ github.event.inputs.environment }}"
            ARCH="${{ github.event.inputs.architecture }}"
          else
            ENVIRONMENT="sandbox"
            ARCH="arm64"
          fi
          echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
          echo "ARCH=$ARCH" >> $GITHUB_ENV

          # Set ECR repository based on environment
          if [[ "$ENVIRONMENT" == "production" ]]; then
            ECR_REPOSITORY="${PRODUCTION_ECR}"
            ACCOUNT_ID="${PRODUCTION_ACCOUNT_ID}"
          else
            ECR_REPOSITORY="${SANDBOX_ECR}"
            ACCOUNT_ID="${SANDBOX_ACCOUNT_ID}"
          fi
          echo "ECR_REPOSITORY=$ECR_REPOSITORY" >> $GITHUB_ENV
          echo "ACCOUNT_ID=$ACCOUNT_ID" >> $GITHUB_ENV

          # Set the platform based on the matrix arch
          if [[ "$ARCH" == "amd64" ]]; then
            PLATFORM="linux/amd64"
          else
            PLATFORM="linux/arm64"
          fi
          echo "PLATFORM=$PLATFORM" >> $GITHUB_ENV

          # Determine image tags
          if [[ "${{ github.event_name }}" == "workflow_dispatch" && -n "${{ github.event.inputs.image_tag }}" ]]; then
            # Use manually specified tag if provided
            VERSION="${{ github.event.inputs.image_tag }}"
          elif [[ "${{ github.ref_type }}" == "tag" ]]; then
            # If this is a tag, use the tag as the version
            VERSION="${{ github.ref_name }}"
          else
            # Otherwise use default tag 'sandbox' or the short commit hash
            if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
              VERSION="sandbox"
            else
              VERSION="${{ github.sha }}"
              VERSION="${VERSION:0:7}"
            fi
          fi

          # Create image tags
          ARCH_TAG="${VERSION}-$ARCH"
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "ARCH_TAG=$ARCH_TAG" >> $GITHUB_ENV

          echo "Environment: $ENVIRONMENT"
          echo "ECR Repository: $ECR_REPOSITORY"
          echo "Platform: $PLATFORM"
          echo "Version: $VERSION"
          echo "Architecture Tag: $ARCH_TAG"


      - name: Assume role in target account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          # Dynamically change account id based on environment
          role-to-assume: arn:aws:iam::${{ env.ACCOUNT_ID }}:role/github-actions-20degrees-role
          aws-region: us-east-1
          role-session-name: GitHubActionsECRPush
          mask-aws-account-id: true

      - name: Unset AWS_PROFILE
        run: |
          echo "Unsetting AWS_PROFILE"
          unset AWS_PROFILE
          echo "AWS_PROFILE=" >> $GITHUB_ENV


      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
        with:
          mask-password: true


      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          platforms: ${{ env.PLATFORM }}
          tags: |
            ${{ env.ECR_REPOSITORY }}:${{ env.ARCH_TAG }}
            ${{ env.ECR_REPOSITORY }}:${{ env.ENVIRONMENT }}-${{ env.ARCH }}-latest
            ${{ env.ECR_REPOSITORY }}:${{ env.VERSION }}
          build-args: |
            MODEL_DOWNLOAD_TIME=${{ github.run_number }}-${{ github.run_attempt }}
          no-cache: false
