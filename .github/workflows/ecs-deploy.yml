name: Deploy to ECS

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'sandbox'
        type: choice
        options:
          - sandbox
          - production
      image_tag:
        description: 'Image tag to use (default: latest)'
        required: false
        default: 'latest'
        type: string
      service:
        description: 'Service to deploy to'
        required: true
        default: 'ai'
        type: choice
        options:
          - ai

jobs:
  deploy:
    name: Deploy to ECS
    runs-on: ubuntu-latest
    # Only run if triggered manually
    if: ${{ github.event_name == 'workflow_dispatch' }}

    # Required permissions for OIDC authentication
    permissions:
      id-token: write
      contents: read

    # Define environment variables
    env:
      SANDBOX_ACCOUNT_ID: "************"
      PRODUCTION_ACCOUNT_ID: "************"
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set environment variables
        id: set-env
        run: |
          # Set environment from input
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          IMAGE_TAG="${{ github.event.inputs.image_tag || 'latest' }}"
          SERVICE_TYPE="${{ github.event.inputs.service }}"
          echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV
          
          # Set AWS account ID and ECR repository based on environment
          if [[ "$ENVIRONMENT" == "production" ]]; then
            ACCOUNT_ID="${{ env.PRODUCTION_ACCOUNT_ID }}"
            ECR_REPOSITORY="${{ env.PRODUCTION_ECR }}"
          else
            ACCOUNT_ID="${{ env.SANDBOX_ACCOUNT_ID }}"
            ECR_REPOSITORY="${{ env.SANDBOX_ECR }}"
          fi
          echo "ACCOUNT_ID=$ACCOUNT_ID" >> $GITHUB_ENV
          echo "ECR_REPOSITORY=$ECR_REPOSITORY" >> $GITHUB_ENV
          
          echo "Environment: $ENVIRONMENT"
          echo "Account ID: $ACCOUNT_ID"
          echo "ECR Repository: $ECR_REPOSITORY"
          echo "Image Tag: $IMAGE_TAG"
      
      - name: Assume role in target account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          # Dynamically change account id based on environment
          role-to-assume: arn:aws:iam::${{ env.ACCOUNT_ID }}:role/github-actions-20degrees-role
          aws-region: us-east-1
          role-session-name: GitHubActionsECSDeploy
          mask-aws-account-id: true
          
      - name: Unset AWS_PROFILE
        run: |
          echo "Unsetting AWS_PROFILE environment variable"
          # Unset AWS_PROFILE for current shell
          unset AWS_PROFILE
          # Verify it's unset
          echo "AWS_PROFILE is now: '${AWS_PROFILE:-unset}'"

      - name: Verify AWS credentials
        run: |
          echo "Verifying AWS credentials..."
          # Ensure AWS_PROFILE is not set for this verification
          unset AWS_PROFILE
          echo "Current AWS environment:"
          echo "  AWS_REGION: ${AWS_REGION:-not set}"
          echo "  AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-not set}"
          echo "  AWS_PROFILE: ${AWS_PROFILE:-not set}"
          echo "  AWS_ACCESS_KEY_ID: $([ -n "${AWS_ACCESS_KEY_ID:-}" ] && echo "set" || echo "not set")"
          echo "  AWS_SESSION_TOKEN: $([ -n "${AWS_SESSION_TOKEN:-}" ] && echo "set" || echo "not set")"
          echo ""
          echo "Testing AWS CLI..."
          aws sts get-caller-identity
          echo "AWS credentials verified successfully"

      - name: Wait for credentials propagation
        run: |
          echo "Waiting 5 seconds for AWS credentials to fully propagate..."
          sleep 5

      - name: Make deploy script executable
        run: chmod +x ./scripts/ecs-deploy.sh
    
      
      - name: Deploy to ECS
        run: |
          echo "Deploying to ${{ env.ENVIRONMENT }} with image tag ${{ env.IMAGE_TAG }}"
          echo "Service type: ${{ github.event.inputs.service }}"
          echo "ECR Repository: ${{ env.ECR_REPOSITORY }}"
          # Ensure AWS_PROFILE is not set for deployment
          unset AWS_PROFILE
          ./scripts/ecs-deploy.sh -e ${{ env.ENVIRONMENT }} -t ${{ env.IMAGE_TAG }} -s ${{ github.event.inputs.service }}
      
      - name: Notify on success
        if: success()
        run: |
          echo "✅ Successfully deployed to ${{ env.ENVIRONMENT }} environment with image tag ${{ env.IMAGE_TAG }}"
          echo "🚀 Service: ${{ github.event.inputs.service }}"
          echo "🔗 Health check: https://services.${{ env.ENVIRONMENT }}.healthtechgate.com/machinelearning/livecheck"

      - name: Notify on failure
        if: failure()
        run: |
          echo "❌ Failed to deploy to ${{ env.ENVIRONMENT }} environment. Check the workflow logs for details."
          echo "🔍 Service: ${{ github.event.inputs.service }}"
          echo "📋 Image tag: ${{ env.IMAGE_TAG }}"
