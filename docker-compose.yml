services:
  aiagent:
    build: .
    container_name: aiagent
    env_file:
      - .env
    environment:
      - NEW_RELIC_LICENSE_KEY=${NEW_RELIC_LICENSE_KEY}
      - NEW_RELIC_APP_NAME=${NEW_RELIC_APP_NAME}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENSEARCH_HOST=${OPENSEARCH_HOST}
      - MONGO_URI=${MONGO_URI}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - JWT_SECRET=${JWT_SECRET}
      - ENVIRONMENT=${ENVIRONMENT}
    ports:
      - "8000:8000"
    restart: unless-stopped
