import os
import traceback
from uuid import uuid4
from typing import Optional
from datetime import datetime, timedelta
from handlers.helpers import extract_response_message
from handlers.tools import store_user_recommendations
from fastapi import FastAPI, APIRouter, Request, HTTPException, Query

from models import *
from handlers import agent, auth, tools

from cluster import process_faq_clusters

try:
    # Determine which New Relic config file to use based on environment
    # Check both ENVIRONMENT and NEW_RELIC_ENVIRONMENT variables for compatibility
    environment = os.getenv('NEW_RELIC_ENVIRONMENT', os.getenv('ENVIRONMENT', 'sandbox')).lower()
    
    # Get the license key from environment or use default
    license_key = os.getenv('NEW_RELIC_LICENSE_KEY', os.getenv('NEW_RELIC_LICENSE_KEY'))
    
    # Set app name based on environment
    if environment == 'production':
        app_name = ('20DegreesAiAgent-Production')
        print(f"Setting up New Relic for PRODUCTION environment with app name: {app_name}")
    else:
        app_name = ('20DegreesAiAgent-Sandbox')
        print(f"Setting up New Relic for SANDBOX environment with app name: {app_name}")
    
    # Create or update the newrelic.ini file dynamically
    newrelic_config = f"""# ---------------------------------------------------------------------------
#
# This file configures the New Relic Python Agent.
#
# ---------------------------------------------------------------------------

[newrelic]
# License key
license_key = {license_key}

# Application name
app_name = {app_name}

# When "true", the agent collects performance data about your application
monitor_mode = true

# Log level
log_level = {os.getenv('LOG_LEVEL', 'info')}

# Log to stdout for better container logging
log_file = stdout

# High Security Mode
high_security = false

# Transaction tracer settings
transaction_tracer.enabled = true
transaction_tracer.transaction_threshold = apdex_f
transaction_tracer.record_sql = obfuscated
transaction_tracer.stack_trace_threshold = 0.5
transaction_tracer.explain_enabled = true
transaction_tracer.explain_threshold = 0.5

# Error collector settings
error_collector.enabled = true

# Browser monitoring
browser_monitoring.auto_instrument = true

# Thread profiler
thread_profiler.enabled = true

# Distributed tracing for better ECS visibility
distributed_tracing.enabled = true

# ECS-specific configuration
utilization.detect_aws = true
utilization.detect_docker = true

# AWS ECS metadata endpoint configuration
aws.ecs_metadata_uri_v4 = true
aws.metadata_service_timeout = 0.8

# AWS SDK instrumentation
instrumentation.boto3 = true
instrumentation.botocore = true

# Custom attributes for ECS environment
attributes.include = aws.ecs.* request.parameters.* http.statusCode
"""
    
    # Write the dynamically generated config to newrelic.ini
    with open('newrelic.ini', 'w') as f:
        f.write(newrelic_config)
    
    print(f"Created dynamic newrelic.ini with app_name={app_name}")
    
    # Initialize New Relic with the dynamically created config file
    import newrelic.agent
    newrelic.agent.initialize('newrelic.ini')
    print("New Relic initialization successful")
    
except Exception as e:
    print(f"Error initializing New Relic: {str(e)}")
    print(f"Exception type: {type(e).__name__}")
    print(f"Exception traceback: {traceback.format_exc()}")

# Initialize logging
import logging
import sys
from starlette.status import HTTP_403_FORBIDDEN
from fastapi.exception_handlers import http_exception_handler
from starlette.exceptions import HTTPException as StarletteHTTPException

# Configure custom logger to avoid conflicts with Uvicorn's access logger
logging.basicConfig(level=logging.INFO)
app_logger = logging.getLogger("app")

# Create a handler that writes to stderr
handler = logging.StreamHandler(sys.stderr)
handler.setFormatter(logging.Formatter('%(levelname)s:     %(message)s'))
app_logger.addHandler(handler)
app_logger.propagate = False  # Prevent propagation to root logger

# Initialize FastAPI app
app = FastAPI()

# Custom exception handler for 403 errors
@app.exception_handler(StarletteHTTPException)
async def custom_http_exception_handler(request, exc):
    # Log 403 errors as ERROR level instead of INFO
    if exc.status_code == HTTP_403_FORBIDDEN:
        # Try to get the request body
        body = b""
        try:
            # Save the original receive function
            original_receive = request.scope.get("_receive", request.receive)
            
            # Define a new receive function that returns the cached body
            async def receive():
                nonlocal body
                message = await original_receive()
                if message["type"] == "http.request" and "body" in message:
                    body += message["body"]
                return message
            
            # Replace the receive function
            request.scope["_receive"] = receive
            
            # Try to get the body if it hasn't been read yet
            if hasattr(request, "_body"):
                body = request._body
            else:
                body_bytes = await request.body()
                body = body_bytes
                
            # Log the request details with body
            try:
                body_str = body.decode('utf-8')
                app_logger.error(f"{request.client.host}:{request.client.port} - \"{request.method} {request.url.path} HTTP/{request.scope['http_version']}\" {exc.status_code} Forbidden - Request Body: {body_str}")
            except:
                # If body can't be decoded, log it as bytes
                app_logger.error(f"{request.client.host}:{request.client.port} - \"{request.method} {request.url.path} HTTP/{request.scope['http_version']}\" {exc.status_code} Forbidden - Request Body: [binary data]")
        except Exception as e:
            # If we can't get the body, log without it
            app_logger.error(f"{request.client.host}:{request.client.port} - \"{request.method} {request.url.path} HTTP/{request.scope['http_version']}\" {exc.status_code} Forbidden - Error getting request body: {str(e)}")
    
    # Continue with the default exception handler
    return await http_exception_handler(request, exc)

# Router with the prefix
router = APIRouter(prefix="/agent")

# Health check endpoint
@router.get("/healthcheck")
def healthcheck():
    return {"status": "healthy"}

# Chat endpoint
@router.post("/api/v1/chat")
def chat(input_body: ChatRequest, request: Request, user_guid: Optional[str] = None):
    # Log the incoming request body
    app_logger.info(f"Received request to /agent/api/v1/chat - Body: {input_body.dict()}")
    
    x_access_token = request.headers.get('x-access-token')
    try:
        jwt_payload = auth.decodeJWT(x_access_token)
    except Exception as auth_error:
        # Log the authentication error with details
        app_logger.error(f"Authentication failed: {str(auth_error)} - Request Body: {input_body.dict()}")
        raise HTTPException(status_code=403, detail="Invalid token or expired token.")
    
    if jwt_payload:
        request.state.userId = jwt_payload["user_guid"]
        request.state.token = x_access_token
    else:
        # Log the invalid token error with request details
        app_logger.error(f"Invalid or expired token - Request Body: {input_body.dict()}")
        raise HTTPException(status_code=403, detail="Invalid token or expired token.")
    user_id = user_guid if user_guid else request.state.userId
    print("Request from user Id:", user_id)
    print(f"Received message: {input_body.message}")
    try:
        response = agent.ask_question(user_id, query=input_body.message, access_token=request.state.token)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Chat History endpoint
@router.get("/api/v1/chat-history")
def get_chat_history(
    request: Request, user_guid: Optional[str] = None, 
    query: Optional[str] = None, page: int = 1, limit: int = 5
):
    # Log the incoming request parameters
    request_params = {
        "user_guid": user_guid,
        "query": query,
        "page": page,
        "limit": limit
    }
    app_logger.info(f"Received request to /agent/api/v1/chat-history - Params: {request_params}")
    
    try:
        x_access_token = request.headers.get('x-access-token')
        try:
            jwt_payload = auth.decodeJWT(x_access_token)
        except Exception as auth_error:
            # Log the authentication error with details
            app_logger.error(f"Authentication failed: {str(auth_error)} - Request Params: {request_params}")
            raise HTTPException(status_code=403, detail="Invalid token or expired token.")
        
        if jwt_payload:
            request.state.userId = jwt_payload["user_guid"]
            request.state.token = x_access_token
        else:
            # Log the invalid token error with request details
            app_logger.error(f"Invalid or expired token - Request Params: {request_params}")
            raise HTTPException(status_code=403, detail="Invalid token or expired token.")
        user_id = user_guid if user_guid else request.state.userId
        print("Request from user Id:", user_id)
        print(f"Received message: {query}")

        results = tools.retrieve_chat_history(user_id, query, page, limit)
        return { "results": results, "page": page, "limit": limit }
    except Exception as e:
        print("Error occured while retreiving chat history", e)
        return { "success": False, "error": "Error occured while retreiving chat history" }

# Chat Feedback
@router.patch("/api/v1/chat/{message_id}")
async def update_feedback(message_id: str, feedback_update: FeedbackUpdate):
    success = tools.update_message_feedback(message_id, feedback_update.feedback)
    if not success:
        raise HTTPException(status_code=404, detail="Message not found")
    
    return {"message": "Feedback updated successfully"}


msg_type = {
    "nudge": """You are a helpful and motivating wellness coach helping users stay consistent with their weekly health targets. Use the "profile" and "target_achievements" tools to understand the user's 90 day goals and weekly habits over the past 4 weeks. 
Your task is to generate a single nudge Highlighting progress — mention any improvements or positive trends while offering encouragement — celebrate consistency and effort. And suggest an action — either recommend continuing the habit, slightly adjusting targets, or celebrating completion.
The response should be in JSON format:
{{
  "title": "Some encouraging title",
  "message": "Acknowledges the user's effort, highlights specific areas where targets were not met last week and gently nudges them to focus on these areas.",
  "priority": "low,medium,high"
}}
Keep the tone supportive, brief, and goal-focused. Use tools to query user logs. Ensure your message is personalized and data-informed.
""",
    "insight": """You are a wellness insights coach. Your task is to analyze the user's recent health and habit tracking data to identify positive behavioral trends or meaningful changes for the category: {category}.
Goal: Create a short, personalized, data-driven insight that:
- Highlights any positive trend or improvement from the past week or more.
- Reinforces this behavior to encourage continued progress.
- Suggests a next-step action to build upon this improvement.
Avoid generic praise—base everything on actual user data. Format: {{ "title": "...", "message": "...", "priority": "low,medium,high"}}""",
    "anomaly": """You are a wellness monitoring assistant. Review the user's recent health and habit tracking data for category: {category} to identify behavioral anomalies or deviations from usual patterns.
Look for issues like missing logs, data gaps, sugar spikes/dips, or sudden drops in activity/sleep.
Goal: Return a JSON response that:
- Clearly describes the anomaly
- Encourages reflection and suggests one concrete action for improvement
Ensure the response is accurate and data-informed. Format: {{ "title": "...", "message": "...", "priority": "low,medium,high"}}
"""
}

# Nudge endpoint
@router.post("/api/v1/nudge")
def nudge_chat(input_body: NudgeRequest, request: Request, user_guid: Optional[str] = None):
    # Log the incoming request body
    app_logger.info(f"Received request to /agent/api/v1/nudge - Body: {input_body.dict()}")
    
    x_access_token = request.headers.get('x-access-token')
    try:
        jwt_payload = auth.decodeJWT(x_access_token)
    except Exception as auth_error:
        # Log the authentication error with details
        app_logger.error(f"Authentication failed: {str(auth_error)} - Request Body: {input_body.dict()}")
        raise HTTPException(status_code=403, detail="Invalid token or expired token.")
    
    if jwt_payload:
        request.state.userId = jwt_payload["user_guid"]
        request.state.token = x_access_token
    else:
        # Log the invalid token error with request details
        app_logger.error(f"Invalid or expired token - Request Body: {input_body.dict()}")
        raise HTTPException(status_code=403, detail="Invalid token or expired token.")
    user_id = user_guid if user_guid else request.state.userId
    print("Request from user Id:", user_id)

    print(f"Received message: {input_body.type}, {input_body.category}")
    try:
        prompt = msg_type[input_body.type].format(category=input_body.category)

        response = agent.nudge_and_insights(user_id, query=prompt)
        response = extract_response_message(response)
        
        # Generate timestamp and TTL
        now = datetime.utcnow()
        ttl = now + timedelta(days=3)

        # Build OpenSearch document
        document = {
            "id": str(uuid4()),
            "userId": user_id,
            "type": input_body.type,
            "content": {
                "title": response["title"],
                "message": response["message"]
            },
            "timestamp": now.isoformat() + "Z",
            "category": input_body.category,
            "priority": response.get("priority", "low"),
            "ttl": ttl.isoformat() + "Z",
            "status": "open"
        }

        # Index the document
        store_user_recommendations(document)
        return {"response": document}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def nudge(user_id, type, category):
    print(f"Received message: {user_id}, {type}, {category}")
    try:
        prompt = msg_type[type]
        try: prompt = prompt.format(category=category)
        except: pass
        response = agent.nudge_and_insights(user_id, query=prompt)
        response = extract_response_message(response)
        
        # Generate timestamp and TTL
        now = datetime.utcnow()
        ttl = now + timedelta(days=3)

        # Build OpenSearch document
        document = {
            "id": str(uuid4()),
            "userId": user_id,
            "type": type,
            "content": {
                "title": response["title"],
                "message": response["message"]
            },
            "timestamp": now.isoformat() + "Z",
            "category": category,
            "priority": response.get("priority", "low"),
            "ttl": ttl.isoformat() + "Z",
            "status": "open"
        }

        # Index the document
        store_user_recommendations(document)
        print(document)

    except Exception as e:
        print("Error occured: ", e)

# Clustering Questions
@router.get("/top-questions")
def get_top_questions():
    try:
        results = process_faq_clusters()
        return {"top_questions": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Lambda handler
app.include_router(router)
