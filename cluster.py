from opensearchpy import OpenSearch, RequestsHttpConnection
from requests_aws4auth import AWS4Auth
import numpy as np
import os, boto3
import hdbscan
from sklearn.metrics.pairwise import cosine_similarity
import heapq
from typing import List, Tuple, Dict, Generator

# --- Configuration ---
OPENSEARCH_HOST =  os.environ.get("OPENSEARCH_HOST")
INDEX_NAME = "chat-memory"

# AWS Configuration
AWS_REGION = "us-east-1"
AWS_SERVICE = "es"

# Get AWS credentials from environment variables or IAM role
session = boto3.Session()
credentials = session.get_credentials()
aws_auth = AWS4Auth(credentials.access_key, credentials.secret_key, 
                     AWS_REGION, AWS_SERVICE, session_token=credentials.token)

# --- Client Setup ---
def get_opensearch_client() -> OpenSearch:
    return OpenSearch(
        hosts=[{"host": OPENSEARCH_HOST, "port": 443}],
        http_auth=aws_auth,
        use_ssl=True,
        verify_certs=True,
        connection_class=RequestsHttpConnection
    )

# --- Fetch all docs with a scroll API ---
def scroll_all_documents(client: OpenSearch, index: str) -> Generator[dict, None, None]:
    query = {
        "_source": ["messages", "vector"],
        "query": {"exists": {"field": "vector"}},
        "size": 1000
    }
    scroll = "2m"
    page = client.search(index=index, body=query, scroll=scroll)
    scroll_id = page['_scroll_id']
    hits = page['hits']['hits']

    while hits:
        for doc in hits:
            yield doc
        page = client.scroll(scroll_id=scroll_id, scroll=scroll)
        scroll_id = page['_scroll_id']
        hits = page['hits']['hits']

# --- Fetch user messages and vectors ---
def fetch_user_messages_and_vectors(client: OpenSearch, index: str) -> Tuple[List[str], np.ndarray]:
    user_messages = []
    vectors = []

    for doc in scroll_all_documents(client, index):
        source = doc["_source"]
        messages = source.get("messages", [])
        vector = source.get("vector")
        if messages and vector:
            user_messages.append(messages[0])  # Only take the first message
            vectors.append(vector)

    return user_messages, np.array(vectors)

# --- Run clustering ---
def cluster_vectors(vectors: np.ndarray, min_cluster_size: int = 2) -> np.ndarray:
    clusterer = hdbscan.HDBSCAN(min_cluster_size=min_cluster_size, metric='euclidean')
    return clusterer.fit_predict(vectors)

# --- Group messages by cluster ---
def group_by_cluster(labels: np.ndarray, messages: List[str], vectors: np.ndarray) -> Dict[int, List[Tuple[str, np.ndarray]]]:
    clustered = {}
    for idx, label in enumerate(labels):
        if label == -1:
            continue  # noise
        clustered.setdefault(label, []).append((messages[idx], vectors[idx]))
    return clustered

# --- Get top clusters ---
def get_top_clusters(clustered_data: Dict[int, List[Tuple[str, np.ndarray]]], top_n: int = 6) -> List[Tuple[int, List[Tuple[str, np.ndarray]]]]:
    return heapq.nlargest(top_n, clustered_data.items(), key=lambda x: len(x[1]))

# --- Get representative question ---
def get_representative_question(cluster_points: List[Tuple[str, np.ndarray]]) -> Tuple[str, int]:
    questions, vecs = zip(*cluster_points)
    centroid = np.mean(vecs, axis=0).reshape(1, -1)
    similarities = cosine_similarity(vecs, centroid).flatten()
    best_idx = np.argmax(similarities)
    return questions[best_idx], len(questions)

# --- Main Function ---
def process_faq_clusters():
    client = get_opensearch_client()
    user_messages, vectors = fetch_user_messages_and_vectors(client, INDEX_NAME)
    print("Vectors Found", len(user_messages))
    if not vectors.size:
        print("No vectors found.")
        return

    labels = cluster_vectors(vectors)
    print("Got Labels", labels)
    clustered_data = group_by_cluster(labels, user_messages, vectors)
    print("Got Clustered Data", clustered_data)
    top_clusters = get_top_clusters(clustered_data)
    print("Got Top", top_clusters)

    results = []
    for cluster_id, cluster_points in top_clusters:
        question, count = get_representative_question(cluster_points)
        print(question, count)
        results.append({
            "question": question['content'],
            "count": count
        })
    return results
